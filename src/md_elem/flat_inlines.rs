use crate::md_elem::tree::elem::{Inline, SpanVariant, Text, TextVariant};

/// The type of formatting to apply to a range of text.
#[derive(Debug, Clone, PartialEq)]
pub(crate) enum FormattingType {
    /// Standard span formatting (emphasis, strong, delete)
    Span(SpanVariant),
    /// Unknown/atomic content that cannot be processed by regex replacement
    Unknown,
}

/// A flattened representation of inline markdown that separates plain text from formatting.
///
/// This allows regex operations to work on the plain text while preserving formatting
/// information that can be reapplied after the text transformation.
#[derive(Debug, Clone, PartialEq)]
pub struct FlattenedText {
    /// The plain text content with all formatting removed
    pub text: String,
    /// Formatting events that describe where formatting should be applied to the text
    pub formatting_events: Vec<FormattingEvent>,
}

/// Describes a span of formatting that should be applied to a range of characters.
///
/// Events are applied in order, so earlier events become outer spans in nested formatting.
#[derive(Debu<PERSON>, <PERSON><PERSON>, PartialEq)]
pub struct FormattingEvent {
    /// Starting character position in the flattened text (inclusive)
    pub start_pos: usize,
    /// The number of characters this formatting applies to
    pub length: usize,
    /// The type of formatting to apply
    pub formatting: FormattingType,
}

/// Error that occurs when trying to flatten inlines that contain non-flattenable content.
#[derive(Debug)]
pub struct FlattenError {
    // TODO: Add specific error variants and details
}

/// Error that occurs during regex replacement operations.
#[derive(Debug)]
pub struct RegexReplaceError {
    // TODO: Add specific error variants and details
}

impl FlattenedText {
    /// Creates a flattened representation from a slice of inline elements.
    ///
    /// This extracts all plain text content and creates formatting events that describe
    /// where spans should be applied. Links, images, and non-plain text variants will
    /// cause a FlattenError.
    pub(crate) fn from_inlines(inlines: &[Inline]) -> Result<Self, FlattenError> {
        let mut text = String::new();
        let mut formatting_events = Vec::new();

        flatten_inlines_recursive(inlines, &mut text, &mut formatting_events)?;

        Ok(FlattenedText {
            text,
            formatting_events,
        })
    }
}

/// Recursively flattens inlines, building up the text and formatting events.
fn flatten_inlines_recursive(
    inlines: &[Inline],
    text: &mut String,
    formatting_events: &mut Vec<FormattingEvent>,
) -> Result<(), FlattenError> {
    for inline in inlines {
        match inline {
            Inline::Text(Text {
                variant: TextVariant::Plain,
                value,
            }) => {
                text.push_str(value);
            }
            Inline::Text(non_plain_text) => {
                // Non-plain text (code, math, html) - treat as unknown
                let start_pos = text.len();
                text.push_str(&non_plain_text.value);
                let length = non_plain_text.value.len();

                if length > 0 {
                    formatting_events.push(FormattingEvent {
                        start_pos,
                        length,
                        formatting: FormattingType::Unknown,
                    });
                }
            }
            Inline::Span(span) => {
                let start_pos = text.len();

                // Recursively process the span's children
                flatten_inlines_recursive(&span.children, text, formatting_events)?;

                let length = text.len() - start_pos;
                if length > 0 {
                    formatting_events.push(FormattingEvent {
                        start_pos,
                        length,
                        formatting: FormattingType::Span(span.variant),
                    });
                }
            }
            Inline::Link(_) | Inline::Image(_) | Inline::Footnote(_) => {
                // These are atomic and not supported for regex replacement
                return Err(FlattenError {});
            }
        }
    }

    Ok(())
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::md_elem::tree::elem::Span;

    #[test]
    fn test_plain_text_only() {
        let inlines = vec![text("hello world")];
        let result = FlattenedText::from_inlines(&inlines).unwrap();

        assert_eq!(result.text, "hello world");
        assert_eq!(result.formatting_events, vec![]);
    }

    #[test]
    fn test_simple_emphasis() {
        let inlines = vec![text("before "), em(vec![text("emphasized")]), text(" after")];
        let result = FlattenedText::from_inlines(&inlines).unwrap();

        assert_eq!(result.text, "before emphasized after");
        assert_eq!(
            result.formatting_events,
            vec![FormattingEvent {
                start_pos: 7,
                length: 10,
                formatting: SpanVariant::Emphasis,
            }]
        );
    }

    #[test]
    fn test_nested_formatting() {
        // _**text**_
        let inlines = vec![em(vec![strong(vec![text("text")])])];
        let result = FlattenedText::from_inlines(&inlines).unwrap();

        assert_eq!(result.text, "text");
        assert_eq!(
            result.formatting_events,
            vec![
                FormattingEvent {
                    start_pos: 0,
                    length: 4,
                    formatting: FormattingType::Span(SpanVariant::Strong),
                },
                FormattingEvent {
                    start_pos: 0,
                    length: 4,
                    formatting: FormattingType::Span(SpanVariant::Emphasis),
                }
            ]
        );
    }

    fn text(s: &str) -> Inline {
        Inline::Text(Text {
            variant: TextVariant::Plain,
            value: s.to_string(),
        })
    }

    fn em(children: Vec<Inline>) -> Inline {
        Inline::Span(Span {
            variant: SpanVariant::Emphasis,
            children,
        })
    }

    fn strong(children: Vec<Inline>) -> Inline {
        Inline::Span(Span {
            variant: SpanVariant::Strong,
            children,
        })
    }
}
