use crate::md_elem::tree::elem::{Inline, SpanVariant};

/// A flattened representation of inline markdown that separates plain text from formatting.
///
/// This allows regex operations to work on the plain text while preserving formatting
/// information that can be reapplied after the text transformation.
#[derive(Debug, <PERSON>lone, PartialEq)]
pub struct FlattenedText {
    /// The plain text content with all formatting removed
    pub text: String,
    /// Formatting events that describe where formatting should be applied to the text
    pub formatting_events: Vec<FormattingEvent>,
}

/// Describes a span of formatting that should be applied to a range of characters.
///
/// Events are applied in order, so earlier events become outer spans in nested formatting.
#[derive(Debug, Clone, PartialEq)]
pub struct FormattingEvent {
    /// Starting character position in the flattened text (inclusive)
    pub start_pos: usize,
    /// Number of characters this formatting applies to
    pub length: usize,
    /// The type of formatting to apply
    pub formatting: SpanVariant,
}

/// Error that occurs when trying to flatten inlines that contain non-flattenable content.
#[derive(Debug)]
pub struct FlattenError {
    // TODO: Add specific error variants and details
}

/// Error that occurs during regex replacement operations.
#[derive(Debug)]
pub struct RegexReplaceError {
    // TODO: Add specific error variants and details
}
